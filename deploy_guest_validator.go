package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"strings"
)

// DeployGuestValidator 部署客户机验证脚本
func DeployGuestValidator(hardwareID, outputPath string) error {
	log.Printf("开始部署客户机验证脚本...")
	log.Printf("硬件ID: %s", hardwareID[:8]+"...")
	log.Printf("输出路径: %s", outputPath)

	// 读取模板文件
	templatePath := "guest_validator.bat"
	content, err := ioutil.ReadFile(templatePath)
	if err != nil {
		return fmt.Errorf("读取模板文件失败: %w", err)
	}

	// 替换硬件ID
	contentStr := string(content)
	contentStr = strings.ReplaceAll(contentStr, "REPLACE_WITH_ACTUAL_HARDWARE_ID", hardwareID)

	// 确保输出目录存在
	outputDir := filepath.Dir(outputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("创建输出目录失败: %w", err)
	}

	// 写入新文件
	if err := ioutil.WriteFile(outputPath, []byte(contentStr), 0644); err != nil {
		return fmt.Errorf("写入文件失败: %w", err)
	}

	log.Printf("客户机验证脚本部署成功: %s", outputPath)
	return nil
}

// CreateStartupScript 创建开机启动脚本
func CreateStartupScript(validatorPath, startupPath string) error {
	log.Printf("创建开机启动脚本...")

	startupScript := fmt.Sprintf(`@echo off
REM 认证服务自动启动脚本
REM 此脚本将在系统启动时自动运行验证程序

REM 等待系统完全启动
timeout /t 10 /nobreak > nul

REM 运行验证脚本
call "%s"

REM 删除自身（可选）
REM del "%%~f0"
`, validatorPath)

	// 确保启动脚本目录存在
	startupDir := filepath.Dir(startupPath)
	if err := os.MkdirAll(startupDir, 0755); err != nil {
		return fmt.Errorf("创建启动脚本目录失败: %w", err)
	}

	// 写入启动脚本
	if err := ioutil.WriteFile(startupPath, []byte(startupScript), 0644); err != nil {
		return fmt.Errorf("写入启动脚本失败: %w", err)
	}

	log.Printf("开机启动脚本创建成功: %s", startupPath)
	return nil
}

// GenerateDeploymentPackage 生成部署包
func GenerateDeploymentPackage(hardwareID, packageDir string) error {
	log.Printf("生成部署包...")

	// 创建包目录
	if err := os.MkdirAll(packageDir, 0755); err != nil {
		return fmt.Errorf("创建包目录失败: %w", err)
	}

	// 部署验证脚本
	validatorPath := filepath.Join(packageDir, "auth_validator.bat")
	if err := DeployGuestValidator(hardwareID, validatorPath); err != nil {
		return fmt.Errorf("部署验证脚本失败: %w", err)
	}

	// 创建启动脚本
	startupPath := filepath.Join(packageDir, "startup.bat")
	if err := CreateStartupScript(validatorPath, startupPath); err != nil {
		return fmt.Errorf("创建启动脚本失败: %w", err)
	}

	// 创建安装说明
	readmePath := filepath.Join(packageDir, "README.txt")
	readme := `认证服务客户机验证程序部署说明
========================================

文件说明：
- auth_validator.bat: 主验证脚本
- startup.bat: 开机启动脚本
- README.txt: 本说明文件

部署步骤：
1. 将 auth_validator.bat 复制到客户机的 C:\Windows\System32\ 目录
2. 将 startup.bat 复制到客户机的启动文件夹：
   %APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup\
   或者
   C:\ProgramData\Microsoft\Windows\Start Menu\Programs\Startup\

3. 重启客户机，验证程序将自动运行

注意事项：
- 确保客户机已安装VMware Tools
- 验证程序会在启动时自动运行
- 如果验证失败，系统将自动关机
- 验证成功后会自动清理痕迹

安全提示：
- 请妥善保管此部署包
- 不要在未授权的系统上使用
- 定期更新硬件ID以提高安全性
`

	if err := ioutil.WriteFile(readmePath, []byte(readme), 0644); err != nil {
		return fmt.Errorf("创建说明文件失败: %w", err)
	}

	// 创建批量部署脚本
	deployScriptPath := filepath.Join(packageDir, "deploy.bat")
	deployScript := `@echo off
echo 认证服务客户机验证程序自动部署脚本
echo =====================================

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误: 需要管理员权限运行此脚本
    pause
    exit /b 1
)

echo 正在部署验证程序...

REM 复制验证脚本到系统目录
copy /Y "auth_validator.bat" "C:\Windows\System32\" >nul
if %errorLevel% neq 0 (
    echo 错误: 复制验证脚本失败
    pause
    exit /b 1
)

REM 复制启动脚本到启动文件夹
copy /Y "startup.bat" "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup\auth_startup.bat" >nul
if %errorLevel% neq 0 (
    echo 错误: 复制启动脚本失败
    pause
    exit /b 1
)

echo 部署完成！
echo 系统将在下次重启时自动运行验证程序。
echo.
echo 按任意键退出...
pause >nul
`

	if err := ioutil.WriteFile(deployScriptPath, []byte(deployScript), 0644); err != nil {
		return fmt.Errorf("创建部署脚本失败: %w", err)
	}

	log.Printf("部署包生成完成: %s", packageDir)
	return nil
}

// 示例使用函数
func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法: deploy_guest_validator <硬件ID> [输出目录]")
		fmt.Println("示例: deploy_guest_validator ABCD-1234-EFGH-5678 ./deployment")
		os.Exit(1)
	}

	hardwareID := os.Args[1]
	outputDir := "./deployment"
	
	if len(os.Args) > 2 {
		outputDir = os.Args[2]
	}

	if err := GenerateDeploymentPackage(hardwareID, outputDir); err != nil {
		log.Fatalf("生成部署包失败: %v", err)
	}

	fmt.Printf("部署包已生成到: %s\n", outputDir)
	fmt.Println("请查看 README.txt 了解部署说明")
}

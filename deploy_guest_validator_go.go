package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"os/exec"
	"path/filepath"
)

// DeployGuestValidatorGo 部署Go版本的客户机验证程序
func DeployGuestValidatorGo(hardwareID, vmxPath string) error {
	log.Printf("开始部署Go版本客户机验证程序...")
	log.Printf("硬件ID: %s", hardwareID[:8]+"...")

	// 1. 构建guest validator
	if err := buildGuestValidator(); err != nil {
		return fmt.Errorf("构建guest validator失败: %w", err)
	}

	// 2. 创建硬件ID文件
	if err := createHardwareIDFile(hardwareID); err != nil {
		return fmt.Errorf("创建硬件ID文件失败: %w", err)
	}

	// 3. 创建启动脚本
	if err := createStartupScript(); err != nil {
		return fmt.Errorf("创建启动脚本失败: %w", err)
	}

	log.Println("Go版本客户机验证程序部署完成")
	return nil
}

// buildGuestValidator 构建guest validator
func buildGuestValidator() error {
	log.Println("正在构建guest validator...")

	// 切换到guest_validator_go目录
	originalDir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("获取当前目录失败: %w", err)
	}
	defer os.Chdir(originalDir)

	guestValidatorDir := "guest_validator_go"
	if err := os.Chdir(guestValidatorDir); err != nil {
		return fmt.Errorf("切换到guest validator目录失败: %w", err)
	}

	// 设置环境变量
	env := os.Environ()
	env = append(env, "GOOS=windows", "GOARCH=amd64", "CGO_ENABLED=0")

	// 执行go build
	cmd := exec.Command("go", "build", "-ldflags", "-s -w", "-o", "guest_validator.exe", "main.go")
	cmd.Env = env
	
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("go build失败: %w\n输出: %s", err, string(output))
	}

	// 检查生成的文件
	if _, err := os.Stat("guest_validator.exe"); err != nil {
		return fmt.Errorf("guest_validator.exe未生成: %w", err)
	}

	log.Println("guest validator构建成功")
	return nil
}

// createHardwareIDFile 创建硬件ID文件模板
func createHardwareIDFile(hardwareID string) error {
	log.Println("创建硬件ID文件模板...")

	// 创建部署目录
	deployDir := "deploy"
	if err := os.MkdirAll(deployDir, 0755); err != nil {
		return fmt.Errorf("创建部署目录失败: %w", err)
	}

	// 创建硬件ID文件
	hwIDFile := filepath.Join(deployDir, "hardware.id")
	if err := ioutil.WriteFile(hwIDFile, []byte(hardwareID), 0600); err != nil {
		return fmt.Errorf("写入硬件ID文件失败: %w", err)
	}

	log.Printf("硬件ID文件已创建: %s", hwIDFile)
	return nil
}

// createStartupScript 创建启动脚本
func createStartupScript() error {
	log.Println("创建启动脚本...")

	deployDir := "deploy"
	scriptContent := `@echo off
REM Guest Validator Go版本启动脚本

echo 启动Guest Validator...

REM 设置工作目录
cd /d "%~dp0"

REM 复制硬件ID文件到用户目录
set USER_LSW_DIR=%USERPROFILE%\.lsw
if not exist "%USER_LSW_DIR%" mkdir "%USER_LSW_DIR%"

REM 只有在硬件ID文件不存在时才复制
if not exist "%USER_LSW_DIR%\hardware.id" (
    copy "hardware.id" "%USER_LSW_DIR%\hardware.id" >nul 2>&1
)

REM 运行验证程序
guest_validator.exe

REM 检查退出代码
if %ERRORLEVEL% neq 0 (
    echo 验证失败，系统将关机
    shutdown /s /t 5 /f /c "认证验证失败"
) else (
    echo 验证成功
)
`

	scriptFile := filepath.Join(deployDir, "start_validator.bat")
	if err := ioutil.WriteFile(scriptFile, []byte(scriptContent), 0755); err != nil {
		return fmt.Errorf("写入启动脚本失败: %w", err)
	}

	log.Printf("启动脚本已创建: %s", scriptFile)
	return nil
}

// copyGuestValidatorExecutable 复制guest validator可执行文件
func copyGuestValidatorExecutable() error {
	log.Println("复制guest validator可执行文件...")

	srcFile := filepath.Join("guest_validator_go", "guest_validator.exe")
	dstFile := filepath.Join("deploy", "guest_validator.exe")

	// 读取源文件
	data, err := ioutil.ReadFile(srcFile)
	if err != nil {
		return fmt.Errorf("读取源文件失败: %w", err)
	}

	// 写入目标文件
	if err := ioutil.WriteFile(dstFile, data, 0755); err != nil {
		return fmt.Errorf("写入目标文件失败: %w", err)
	}

	log.Printf("可执行文件已复制: %s", dstFile)
	return nil
}

// CreateDeploymentPackage 创建完整的部署包
func CreateDeploymentPackage(hardwareID string) error {
	log.Println("创建完整的部署包...")

	// 1. 部署Go版本的guest validator
	if err := DeployGuestValidatorGo(hardwareID, ""); err != nil {
		return fmt.Errorf("部署Go版本guest validator失败: %w", err)
	}

	// 2. 复制可执行文件
	if err := copyGuestValidatorExecutable(); err != nil {
		return fmt.Errorf("复制可执行文件失败: %w", err)
	}

	// 3. 创建README文件
	if err := createReadme(); err != nil {
		return fmt.Errorf("创建README失败: %w", err)
	}

	log.Println("部署包创建完成，位于 deploy/ 目录")
	return nil
}

// createReadme 创建README文件
func createReadme() error {
	readmeContent := `# Guest Validator Go版本部署包

## 文件说明
- guest_validator.exe: 主验证程序
- hardware.id: 硬件ID文件
- start_validator.bat: 启动脚本

## 部署步骤
1. 将整个deploy目录复制到虚拟机中
2. 在虚拟机的启动项中添加 start_validator.bat
3. 重启虚拟机进行测试

## 工作原理
1. 程序启动时会从VMware guestVar中获取加密的验证数据
2. 解密并验证硬件ID、时间戳和签名
3. 与本地存储的硬件ID进行比较
4. 验证失败则自动关机

## 注意事项
- 确保虚拟机已安装VMware Tools
- 硬件ID文件会自动复制到用户目录 %USERPROFILE%\.lsw\
- 验证失败会立即关机，请谨慎测试
`

	readmeFile := filepath.Join("deploy", "README.md")
	return ioutil.WriteFile(readmeFile, []byte(readmeContent), 0644)
}

package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"
)

func main() {
	// 初始化日志
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	log.Println("认证服务管理器启动中...")

	// 加载配置
	config, err := LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 生成硬件ID
	hwInfo, err := GetHardwareInfo()
	if err != nil {
		log.Fatalf("获取硬件信息失败: %v", err)
	}
	hardwareID := hwInfo.GetHardwareID()
	log.Printf("硬件ID生成成功: %s", hardwareID[:8]+"...")

	// 初始化VMware管理器
	vmManager, err := NewVMwareManager(config, hardwareID)
	if err != nil {
		log.Fatalf("初始化VMware管理器失败: %v", err)
	}

	// 初始化系统托盘
	trayManager, err := NewTrayManager(vmManager)
	if err != nil {
		log.Fatalf("初始化系统托盘失败: %v", err)
	}

	// 设置信号处理
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动托盘（在goroutine中运行）
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("系统托盘panic: %v", r)
			}
			cancel() // 托盘退出时取消上下文
		}()

		if err := trayManager.Run(); err != nil {
			log.Printf("系统托盘运行错误: %v", err)
		}
	}()

	// 主循环
	log.Println("认证服务管理器已启动，系统托盘可用")

	select {
	case sig := <-sigChan:
		log.Printf("收到信号 %v，正在关闭...", sig)
		cancel()
	case <-ctx.Done():
		log.Println("程序被取消")
	}

	// 清理资源
	log.Println("正在清理资源...")
	trayManager.Quit()

	// 等待一段时间确保清理完成
	time.Sleep(1 * time.Second)
	log.Println("认证服务管理器已关闭")
}

package crypto

import (
	"crypto/cipher"
	"crypto/des"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"io"
	"strconv"
	"strings"
	"time"
)

// DES加密密钥种子
const desKeySeed = "zzzznetllllin8888k"

// generateDESKey 生成DES密钥（8字节）
func generateDESKey() []byte {
	hash := sha256.Sum256([]byte(desKeySeed))
	return hash[:8] // DES需要8字节密钥
}

// GenerateSignature 生成签名
func GenerateSignature(hardwareID string, timestamp int64) string {
	data := fmt.Sprintf("%s:%d", hardwareID, timestamp)
	hash := sha256.Sum256([]byte(data + "signature-salt"))
	return hex.EncodeToString(hash[:16]) // 取前16字节作为签名
}

// CreateDynamicGuestVar 创建动态的guestVar值
func CreateDynamicGuestVar(hardwareID string) (string, error) {
	// 获取当前时间戳
	timestamp := time.Now().Unix()

	// 生成签名
	signature := GenerateSignature(hardwareID, timestamp)

	// 组合数据：hardwareID + timestamp + signature
	data := fmt.Sprintf("%s|%d|%s", hardwareID, timestamp, signature)

	// DES加密
	encrypted, err := EncryptDES(data)
	if err != nil {
		return "", fmt.Errorf("DES加密失败: %w", err)
	}

	return encrypted, nil
}

// EncryptDES 使用DES加密数据
func EncryptDES(plaintext string) (string, error) {
	key := generateDESKey()

	// 创建DES cipher
	block, err := des.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("创建DES cipher失败: %w", err)
	}

	// 填充数据到8字节的倍数
	paddedData := pkcs5Padding([]byte(plaintext), block.BlockSize())

	// 生成随机IV
	iv := make([]byte, block.BlockSize())
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return "", fmt.Errorf("生成IV失败: %w", err)
	}

	// 创建CBC模式
	mode := cipher.NewCBCEncrypter(block, iv)

	// 加密数据
	ciphertext := make([]byte, len(paddedData))
	mode.CryptBlocks(ciphertext, paddedData)

	// 将IV和密文组合
	result := append(iv, ciphertext...)

	// 编码为base64
	return base64.StdEncoding.EncodeToString(result), nil
}

// DecryptDES 使用DES解密数据
func DecryptDES(ciphertext string) (string, error) {
	key := generateDESKey()

	// 解码base64
	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", fmt.Errorf("base64解码失败: %w", err)
	}

	// 创建DES cipher
	block, err := des.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("创建DES cipher失败: %w", err)
	}

	// 检查数据长度
	if len(data) < block.BlockSize() {
		return "", fmt.Errorf("密文数据太短")
	}

	// 分离IV和密文
	iv := data[:block.BlockSize()]
	cipherData := data[block.BlockSize():]

	// 检查密文长度是否为块大小的倍数
	if len(cipherData)%block.BlockSize() != 0 {
		return "", fmt.Errorf("密文长度不正确")
	}

	// 创建CBC模式
	mode := cipher.NewCBCDecrypter(block, iv)

	// 解密数据
	plaintext := make([]byte, len(cipherData))
	mode.CryptBlocks(plaintext, cipherData)

	// 去除填充
	unpaddedData, err := pkcs5Unpadding(plaintext)
	if err != nil {
		return "", fmt.Errorf("去除填充失败: %w", err)
	}

	return string(unpaddedData), nil
}

// pkcs5Padding PKCS5填充
func pkcs5Padding(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padtext := make([]byte, padding)
	for i := range padtext {
		padtext[i] = byte(padding)
	}
	return append(data, padtext...)
}

// pkcs5Unpadding PKCS5去填充
func pkcs5Unpadding(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return nil, fmt.Errorf("数据为空")
	}

	padding := int(data[len(data)-1])
	if padding > len(data) || padding == 0 {
		return nil, fmt.Errorf("填充数据无效")
	}

	// 验证填充
	for i := len(data) - padding; i < len(data); i++ {
		if data[i] != byte(padding) {
			return nil, fmt.Errorf("填充数据不一致")
		}
	}

	return data[:len(data)-padding], nil
}

// parseDynamicGuestVar 解析动态guestVar值
func parseDynamicGuestVar(encryptedData string) (hardwareID string, timestamp int64, signature string, err error) {
	// DES解密
	decrypted, err := DecryptDES(encryptedData)
	if err != nil {
		return "", 0, "", fmt.Errorf("DES解密失败: %w", err)
	}

	// 解析数据：hardwareID|timestamp|signature
	parts := strings.Split(decrypted, "|")
	if len(parts) != 3 {
		return "", 0, "", fmt.Errorf("数据格式错误")
	}

	hardwareID = parts[0]
	timestamp, err = strconv.ParseInt(parts[1], 10, 64)
	if err != nil {
		return "", 0, "", fmt.Errorf("时间戳解析失败: %w", err)
	}
	signature = parts[2]

	return hardwareID, timestamp, signature, nil
}

// VerifySignature 验证签名
func VerifySignature(hardwareID string, timestamp int64, signature string) bool {
	expectedSignature := GenerateSignature(hardwareID, timestamp)
	return expectedSignature == signature
}

package main

import (
	"log"
)

func main() {
	// 初始化日志
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	log.Println("认证服务管理器启动中...")

	// 加载配置
	config, err := LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 生成硬件ID
	hwInfo, err := GetHardwareInfo()
	if err != nil {
		log.Fatalf("获取硬件信息失败: %v", err)
	}
	hardwareID := hwInfo.GetHardwareID()
	log.Printf("硬件ID生成成功: %s", hardwareID)

	// 初始化VMware管理器
	vmManager, err := NewVMwareManager(config, hardwareID)
	if err != nil {
		log.Fatalf("初始化VMware管理器失败: %v", err)
	}

	// 初始化系统托盘
	trayManager, err := NewTrayManager(vmManager)
	if err != nil {
		log.Fatalf("初始化系统托盘失败: %v", err)
	}

	// 启动托盘（在goroutine中运行）
	defer func() {
		if r := recover(); r != nil {
			log.Printf("系统托盘panic: %v", r)
		}
	}()

	if err := trayManager.Run(); err != nil {
		log.Printf("系统托盘运行错误: %v", err)
	}

}

package main

import (
	_ "embed"
	"fmt"
	"log"
	"os"
	"sync"
	"time"

	"github.com/getlantern/systray"
	"golang.design/x/clipboard"
)

//go:embed assets/icon.ico
var iconData []byte

// TrayManager 系统托盘管理器
type TrayManager struct {
	vmManager      *VMwareManager
	menuStart      *systray.MenuItem
	menuStop       *systray.MenuItem
	menuRestart    *systray.MenuItem
	menuSuspend    *systray.MenuItem
	menuResume     *systray.MenuItem
	menuStatus     *systray.MenuItem
	menuConfig     *systray.MenuItem
	menuHardwareId *systray.MenuItem
	menuQuit       *systray.MenuItem
	isRunning      bool
	statusTicker   *time.Ticker
	menuMutex      sync.RWMutex // 保护菜单操作的互斥锁
	lastState      VMState      // 缓存上次的状态，避免不必要的更新
	lastUpdate     time.Time    // 上次更新时间，用于防抖
}

// NewTrayManager 创建新的托盘管理器
func NewTrayManager(vmManager *VMwareManager) (*TrayManager, error) {
	return &TrayManager{
		vmManager: vmManager,
		isRunning: false,
		lastState: VMStateUnknown, // 初始化为未知状态
	}, nil
}

// Run 运行系统托盘
func (tm *TrayManager) Run() error {
	systray.Run(tm.onReady, tm.onExit)
	return nil
}

// Quit 退出系统托盘
func (tm *TrayManager) Quit() {
	if tm.statusTicker != nil {
		tm.statusTicker.Stop()
	}
	systray.Quit()
}

// onReady 托盘初始化回调
func (tm *TrayManager) onReady() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("托盘初始化发生panic: %v", r)
		}
	}()

	// 设置托盘图标和标题
	systray.SetIcon(tm.getIconData())
	systray.SetTitle("认证服务")
	systray.SetTooltip("认证服务管理器")

	// 创建菜单项
	tm.createMenuItems()

	// 启动菜单事件处理
	go tm.handleMenuEvents()

	// 启动状态更新定时器
	tm.startStatusUpdater()

	log.Println("系统托盘已初始化")
}

// onExit 托盘退出回调
func (tm *TrayManager) onExit() {
	if tm.statusTicker != nil {
		tm.statusTicker.Stop()
	}
	log.Println("系统托盘已退出")
}

// createMenuItems 创建菜单项
func (tm *TrayManager) createMenuItems() {
	// 状态显示
	tm.menuStatus = systray.AddMenuItem("状态: 检查中...", "认证服务状态")
	tm.menuStatus.Disable()

	systray.AddSeparator()

	// 控制菜单
	tm.menuStart = systray.AddMenuItem("启动认证服务", "启动认证服务")
	tm.menuStop = systray.AddMenuItem("停止认证服务", "停止认证服务")
	tm.menuRestart = systray.AddMenuItem("重启认证服务", "重启认证服务")

	systray.AddSeparator()

	//tm.menuSuspend = systray.AddMenuItem("暂停认证服务", "暂停认证服务")
	//tm.menuResume = systray.AddMenuItem("恢复认证服务", "恢复认证服务")
	//systray.AddSeparator()

	// 配置
	tm.menuConfig = systray.AddMenuItem("配置设置", "打开配置设置")
	tm.menuHardwareId = systray.AddMenuItem("复制硬件ID", "复制硬件ID到剪贴板")

	systray.AddSeparator()
	tm.menuQuit = systray.AddMenuItem("退出", "退出认证服务管理器")
}

// handleMenuEvents 处理菜单事件
func (tm *TrayManager) handleMenuEvents() {
	for {
		select {
		case <-tm.menuStart.ClickedCh:
			tm.handleStartVM()
		case <-tm.menuStop.ClickedCh:
			tm.handleStopVM()
		case <-tm.menuRestart.ClickedCh:
			tm.handleRestartVM()
		//case <-tm.menuSuspend.ClickedCh:
		//	tm.handleSuspendVM()
		//case <-tm.menuResume.ClickedCh:
		//	tm.handleResumeVM()
		case <-tm.menuConfig.ClickedCh:
			tm.handleConfig()
		case <-tm.menuHardwareId.ClickedCh:
			tm.handleShowHardwareId()
		case <-tm.menuQuit.ClickedCh:
			tm.handleQuit()
			return
		}
	}
}

// startStatusUpdater 启动状态更新器
func (tm *TrayManager) startStatusUpdater() {
	// 增加更新间隔，减少对托盘菜单的干扰
	tm.statusTicker = time.NewTicker(10 * time.Second)
	go func() {
		// 立即更新一次状态
		tm.updateStatus()

		for range tm.statusTicker.C {
			tm.updateStatus()
		}
	}()
}

// updateStatus 更新状态显示
func (tm *TrayManager) updateStatus() {
	// 防抖：如果距离上次更新时间太短，则跳过
	now := time.Now()
	if now.Sub(tm.lastUpdate) < 2*time.Second {
		return
	}

	state, err := tm.vmManager.GetVMState()
	if err != nil {
		// 只有状态真的改变时才更新UI
		if tm.lastState != VMStateUnknown {
			tm.menuMutex.Lock()
			tm.menuStatus.SetTitle("状态: 检查失败")
			tm.updateMenuStatesUnsafe(VMStateUnknown)
			tm.lastState = VMStateUnknown
			tm.lastUpdate = now
			tm.menuMutex.Unlock()
		}
		return
	}

	// 只有状态改变时才更新UI，减少不必要的操作
	if tm.lastState != state {
		statusText := fmt.Sprintf("状态: %s", state.String())

		tm.menuMutex.Lock()
		tm.menuStatus.SetTitle(statusText)
		// 更新托盘提示
		systray.SetTooltip(fmt.Sprintf("认证服务管理器 - %s", state.String()))
		tm.updateMenuStatesUnsafe(state)
		tm.lastState = state
		tm.lastUpdate = now
		tm.menuMutex.Unlock()

		log.Printf("状态已更新: %s", state.String())
	}
}

// updateMenuStates 根据虚拟机状态更新菜单项状态（线程安全版本）
func (tm *TrayManager) updateMenuStates(state VMState) {
	tm.menuMutex.Lock()
	defer tm.menuMutex.Unlock()
	tm.updateMenuStatesUnsafe(state)
}

// updateMenuStatesUnsafe 根据虚拟机状态更新菜单项状态（非线程安全，需要外部加锁）
func (tm *TrayManager) updateMenuStatesUnsafe(state VMState) {
	switch state {
	case VMStateRunning:
		tm.menuStart.Disable()
		tm.menuStop.Enable()
		tm.menuRestart.Enable()
		//tm.menuSuspend.Enable()
		//tm.menuResume.Disable()
	case VMStateStopped:
		tm.menuStart.Enable()
		tm.menuStop.Disable()
		tm.menuRestart.Disable()
		//tm.menuSuspend.Disable()
		//tm.menuResume.Disable()
	case VMStateSuspended:
		tm.menuStart.Disable()
		tm.menuStop.Enable()
		tm.menuRestart.Enable()
		//tm.menuSuspend.Disable()
		//tm.menuResume.Enable()
	default:
		tm.menuStart.Disable()
		tm.menuStop.Disable()
		tm.menuRestart.Disable()
		//tm.menuSuspend.Disable()
		//tm.menuResume.Disable()
	}
}

// 菜单事件处理函数
func (tm *TrayManager) handleStartVM() {
	go func() {
		if err := tm.vmManager.StartVM(); err != nil {
			log.Printf("启动认证服务失败: %v", err)
			tm.showNotification("启动失败", fmt.Sprintf("启动认证服务失败: %v", err))
		} else {
			tm.showNotification("启动成功", "认证服务已成功启动")
		}
	}()
}

func (tm *TrayManager) handleStopVM() {
	go func() {
		if err := tm.vmManager.StopVM(); err != nil {
			log.Printf("停止认证服务失败: %v", err)
			tm.showNotification("停止失败", fmt.Sprintf("停止认证服务失败: %v", err))
		} else {
			tm.showNotification("停止成功", "认证服务已成功停止")
		}
	}()
}

func (tm *TrayManager) handleRestartVM() {
	go func() {
		if err := tm.vmManager.RestartVM(); err != nil {
			log.Printf("重启认证服务失败: %v", err)
			tm.showNotification("重启失败", fmt.Sprintf("重启认证服务失败: %v", err))
		} else {
			tm.showNotification("重启成功", "认证服务已成功重启")
		}
	}()
}

func (tm *TrayManager) handleSuspendVM() {
	go func() {
		if err := tm.vmManager.SuspendVM(); err != nil {
			log.Printf("暂停认证服务失败: %v", err)
			tm.showNotification("暂停失败", fmt.Sprintf("暂停认证服务失败: %v", err))
		} else {
			tm.showNotification("暂停成功", "认证服务已暂停")
		}
	}()
}

func (tm *TrayManager) handleResumeVM() {
	go func() {
		if err := tm.vmManager.ResumeVM(); err != nil {
			log.Printf("恢复认证服务失败: %v", err)
			tm.showNotification("恢复失败", fmt.Sprintf("恢复认证服务失败: %v", err))
		} else {
			tm.showNotification("恢复成功", "认证服务已恢复运行")
		}
	}()
}

func (tm *TrayManager) handleConfig() {
	// TODO: 实现配置界面
	tm.showNotification("配置", "配置功能正在开发中...")
}

func (tm *TrayManager) handleQuit() {
	log.Println("用户请求退出程序")
	tm.Quit()
	// 强制退出程序
	os.Exit(0)
}

// showNotification 显示通知（简单实现）
func (tm *TrayManager) showNotification(title, message string) {
	log.Printf("通知 - %s: %s", title, message)
	// 在Windows上可以使用系统通知，这里先用日志代替
}

// safeMenuOperation 安全地执行菜单操作，避免在更新过程中出现问题
func (tm *TrayManager) safeMenuOperation(operation func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("菜单操作发生panic: %v", r)
		}
	}()

	tm.menuMutex.Lock()
	defer tm.menuMutex.Unlock()
	operation()
}

// getIconData 获取托盘图标数据
func (tm *TrayManager) getIconData() []byte {
	// 这里返回一个简单的ICO格式图标数据
	// 在实际项目中，应该使用真实的图标文件
	return iconData
}

func (tm *TrayManager) handleShowHardwareId() {
	// 将硬件ID复制到剪贴板
	err := clipboard.Init()
	if err != nil {
		log.Printf("初始化剪贴板失败: %v", err)
		return
	}
	clipboard.Write()

	tm.showNotification("硬件ID", "硬件ID已复制到剪贴板")
}

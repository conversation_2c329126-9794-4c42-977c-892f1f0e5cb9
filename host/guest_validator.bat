@echo off
REM 认证服务验证脚本
REM 此脚本在客户机启动时自动运行，验证硬件ID

setlocal enabledelayedexpansion

REM 硬编码的预期硬件ID（部署时需要替换为实际的ID）
set EXPECTED_ID=REPLACE_WITH_ACTUAL_HARDWARE_ID

REM 设置日志文件
set LOG_FILE=%TEMP%\auth_validation.log

REM 记录启动时间
echo [%date% %time%] 认证服务验证开始 >> "%LOG_FILE%"

REM 等待系统完全启动
echo [%date% %time%] 等待系统启动完成... >> "%LOG_FILE%"
timeout /t 15 /nobreak > nul

REM 查找rpctool.exe
set RPCTOOL_PATH=""
if exist "C:\Program Files\VMware\VMware Tools\rpctool.exe" (
    set RPCTOOL_PATH="C:\Program Files\VMware\VMware Tools\rpctool.exe"
) else if exist "C:\Program Files (x86)\VMware\VMware Tools\rpctool.exe" (
    set RPCTOOL_PATH="C:\Program Files (x86)\VMware\VMware Tools\rpctool.exe"
) else if exist "C:\VMware\VMware Tools\rpctool.exe" (
    set RPCTOOL_PATH="C:\VMware\VMware Tools\rpctool.exe"
) else (
    echo [%date% %time%] 错误: 未找到rpctool.exe >> "%LOG_FILE%"
    goto SHUTDOWN
)

echo [%date% %time%] 找到rpctool: !RPCTOOL_PATH! >> "%LOG_FILE%"

REM 获取硬件ID
echo [%date% %time%] 获取硬件ID... >> "%LOG_FILE%"
%RPCTOOL_PATH% info-get guestinfo.hardware_id > "%TEMP%\hardware_id.tmp" 2>&1

if %ERRORLEVEL% neq 0 (
    echo [%date% %time%] 错误: 无法获取硬件ID >> "%LOG_FILE%"
    goto SHUTDOWN
)

REM 读取硬件ID
set /p HARDWARE_ID=<"%TEMP%\hardware_id.tmp"
del "%TEMP%\hardware_id.tmp" 2>nul

if "!HARDWARE_ID!"=="" (
    echo [%date% %time%] 错误: 硬件ID为空 >> "%LOG_FILE%"
    goto SHUTDOWN
)

echo [%date% %time%] 获取到硬件ID: !HARDWARE_ID:~0,8!... >> "%LOG_FILE%"

REM 验证硬件ID
if "!HARDWARE_ID!"=="!EXPECTED_ID!" (
    echo [%date% %time%] 硬件ID验证成功 >> "%LOG_FILE%"
    goto CLEANUP
) else (
    echo [%date% %time%] 错误: 硬件ID验证失败 >> "%LOG_FILE%"
    echo [%date% %time%] 预期: !EXPECTED_ID:~0,8!... >> "%LOG_FILE%"
    echo [%date% %time%] 实际: !HARDWARE_ID:~0,8!... >> "%LOG_FILE%"
    goto SHUTDOWN
)

:CLEANUP
echo [%date% %time%] 开始清理验证痕迹... >> "%LOG_FILE%"

REM 重置硬件ID为随机值（安全措施）
set RESET_ID=RESET_%RANDOM%_%RANDOM%_%date:~0,4%%date:~5,2%%date:~8,2%
%RPCTOOL_PATH% info-set guestinfo.hardware_id "!RESET_ID!" 2>nul

REM 清理临时文件
del "%TEMP%\hardware_id.tmp" 2>nul
del "%TEMP%\vmware_validation.tmp" 2>nul

REM 清理环境变量
set HARDWARE_ID=
set EXPECTED_ID=
set RESET_ID=

echo [%date% %time%] 验证完成，系统继续运行 >> "%LOG_FILE%"

REM 可选：删除日志文件以减少痕迹
timeout /t 2 /nobreak > nul
del "%LOG_FILE%" 2>nul

goto END

:SHUTDOWN
echo [%date% %time%] 验证失败，系统将关机 >> "%LOG_FILE%"
echo 认证验证失败，系统将在5秒后关机...

REM 显示错误信息
msg * "认证验证失败，系统即将关机" 2>nul

REM 等待5秒后关机
timeout /t 5 /nobreak
shutdown /s /t 0 /f /c "认证验证失败，未授权访问"

goto END

:END
REM 清理脚本痕迹
set LOG_FILE=
set RPCTOOL_PATH=
endlocal

REM 自删除脚本（可选，增强安全性）
REM (goto) 2>nul & del "%~f0"

package main

import (
	"encoding/json"
	"fmt"
	"license-server-wrapper/crypto"
	"os"
	"path/filepath"
)

// Config 应用程序配置
type Config struct {
	VMwarePath string `json:"vmware_path"` // VMware Workstation安装路径
	VMXPath    string `json:"vmx_path"`    // 虚拟机文件路径
	VMPassword string `json:"vm_password"` // 虚拟机密码（加密存储）
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		VMwarePath: `C:\Program Files (x86)\VMware\VMware Workstation\vmrun.exe`,
		VMXPath:    "",
		VMPassword: "",
	}
}

// LoadConfig 加载配置文件
func LoadConfig() (*Config, error) {
	configPath := getConfigPath()

	// 如果配置文件不存在，创建默认配置
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		config := DefaultConfig()
		if err := config.Save(); err != nil {
			return nil, fmt.Errorf("创建默认配置失败: %w", err)
		}
		return config, nil
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 解密敏感信息
	if config.VMPassword != "" {
		decrypted, err := crypto.DecryptDES(config.VMPassword)
		if err != nil {
			return nil, fmt.Errorf("解密VM密码失败: %w", err)
		}
		config.VMPassword = decrypted
	}

	return &config, nil
}

// Save 保存配置到文件
func (c *Config) Save() error {
	configPath := getConfigPath()

	// 创建配置目录
	configDir := filepath.Dir(configPath)
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return fmt.Errorf("创建配置目录失败: %w", err)
	}

	// 复制配置以避免修改原始数据
	configCopy := *c

	// 加密敏感信息
	if configCopy.VMPassword != "" {
		encrypted, err := crypto.EncryptDES(configCopy.VMPassword)
		if err != nil {
			return fmt.Errorf("加密VM密码失败: %w", err)
		}
		configCopy.VMPassword = encrypted
	}

	// 序列化配置
	data, err := json.MarshalIndent(&configCopy, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}

	// 写入文件
	if err := os.WriteFile(configPath, data, 0600); err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}

	return nil
}

// Validate 验证配置的有效性
func (c *Config) Validate() error {
	if c.VMwarePath == "" {
		return fmt.Errorf("VMware路径不能为空")
	}

	if _, err := os.Stat(c.VMwarePath); os.IsNotExist(err) {
		return fmt.Errorf("VMware路径不存在: %s", c.VMwarePath)
	}

	if c.VMXPath == "" {
		return fmt.Errorf("虚拟机文件路径不能为空")
	}

	if _, err := os.Stat(c.VMXPath); os.IsNotExist(err) {
		return fmt.Errorf("虚拟机文件不存在: %s", c.VMXPath)
	}

	return nil
}

// getConfigPath 获取配置文件路径
func getConfigPath() string {
	// 获取用户主目录
	homeDir, err := os.UserHomeDir()
	if err != nil {
		// 如果获取失败，使用当前目录
		return "config.json"
	}

	// 在用户主目录下创建配置目录
	return filepath.Join(homeDir, ".lsw", "config.json")
}

// UpdateVMXPath 更新虚拟机文件路径
func (c *Config) UpdateVMXPath(path string) error {
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return fmt.Errorf("虚拟机文件不存在: %s", path)
	}

	c.VMXPath = path
	return c.Save()
}

// UpdatePasswords 更新密码
func (c *Config) UpdatePasswords(vmPassword string) error {
	c.VMPassword = vmPassword
	return c.Save()
}

// GetVMRunPath 获取vmrun可执行文件路径
func (c *Config) GetVMRunPath() string {
	return c.VMwarePath
}

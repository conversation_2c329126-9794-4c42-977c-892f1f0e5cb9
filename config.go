package main

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
)

// Config 应用程序配置
type Config struct {
	VMwarePath    string `json:"vmware_path"`    // VMware Workstation安装路径
	VMXPath       string `json:"vmx_path"`       // 虚拟机文件路径
	VMPassword    string `json:"vm_password"`    // 虚拟机密码（加密存储）
	GuestUsername string `json:"guest_username"` // 客户机用户名
	GuestPassword string `json:"guest_password"` // 客户机密码（加密存储）
	AutoStart     bool   `json:"auto_start"`     // 是否自动启动
	CheckInterval int    `json:"check_interval"` // 检查间隔（秒）
	MaxRetries    int    `json:"max_retries"`    // 最大重试次数
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		VMwarePath:    `C:\Program Files (x86)\VMware\VMware Workstation\vmrun.exe`,
		VMXPath:       "",
		VMPassword:    "",
		GuestUsername: "user",
		GuestPassword: "",
		AutoStart:     false,
		CheckInterval: 30,
		MaxRetries:    3,
	}
}

// LoadConfig 加载配置文件
func LoadConfig() (*Config, error) {
	configPath := getConfigPath()

	// 如果配置文件不存在，创建默认配置
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		config := DefaultConfig()
		if err := config.Save(); err != nil {
			return nil, fmt.Errorf("创建默认配置失败: %w", err)
		}
		return config, nil
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 解密敏感信息
	if config.VMPassword != "" {
		decrypted, err := Decrypt(config.VMPassword)
		if err != nil {
			return nil, fmt.Errorf("解密VM密码失败: %w", err)
		}
		config.VMPassword = decrypted
	}

	if config.GuestPassword != "" {
		decrypted, err := Decrypt(config.GuestPassword)
		if err != nil {
			return nil, fmt.Errorf("解密客户机密码失败: %w", err)
		}
		config.GuestPassword = decrypted
	}

	return &config, nil
}

// Save 保存配置到文件
func (c *Config) Save() error {
	configPath := getConfigPath()

	// 创建配置目录
	configDir := filepath.Dir(configPath)
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return fmt.Errorf("创建配置目录失败: %w", err)
	}

	// 复制配置以避免修改原始数据
	configCopy := *c

	// 加密敏感信息
	if configCopy.VMPassword != "" {
		encrypted, err := Encrypt(configCopy.VMPassword)
		if err != nil {
			return fmt.Errorf("加密VM密码失败: %w", err)
		}
		configCopy.VMPassword = encrypted
	}

	if configCopy.GuestPassword != "" {
		encrypted, err := Encrypt(configCopy.GuestPassword)
		if err != nil {
			return fmt.Errorf("加密客户机密码失败: %w", err)
		}
		configCopy.GuestPassword = encrypted
	}

	// 序列化配置
	data, err := json.MarshalIndent(&configCopy, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}

	// 写入文件
	if err := os.WriteFile(configPath, data, 0600); err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}

	return nil
}

// Validate 验证配置的有效性
func (c *Config) Validate() error {
	if c.VMwarePath == "" {
		return fmt.Errorf("VMware路径不能为空")
	}

	if _, err := os.Stat(c.VMwarePath); os.IsNotExist(err) {
		return fmt.Errorf("VMware路径不存在: %s", c.VMwarePath)
	}

	if c.VMXPath == "" {
		return fmt.Errorf("虚拟机文件路径不能为空")
	}

	if _, err := os.Stat(c.VMXPath); os.IsNotExist(err) {
		return fmt.Errorf("虚拟机文件不存在: %s", c.VMXPath)
	}

	if c.GuestUsername == "" {
		return fmt.Errorf("客户机用户名不能为空")
	}

	if c.CheckInterval < 10 {
		return fmt.Errorf("检查间隔不能小于10秒")
	}

	if c.MaxRetries < 1 {
		return fmt.Errorf("最大重试次数不能小于1")
	}

	return nil
}

// getConfigPath 获取配置文件路径
func getConfigPath() string {
	// 获取用户主目录
	homeDir, err := os.UserHomeDir()
	if err != nil {
		// 如果获取失败，使用当前目录
		return "config.json"
	}

	// 在用户主目录下创建配置目录
	return filepath.Join(homeDir, ".lsw", "config.json")
}

// UpdateVMXPath 更新虚拟机文件路径
func (c *Config) UpdateVMXPath(path string) error {
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return fmt.Errorf("虚拟机文件不存在: %s", path)
	}

	c.VMXPath = path
	return c.Save()
}

// UpdatePasswords 更新密码
func (c *Config) UpdatePasswords(vmPassword, guestPassword string) error {
	c.VMPassword = vmPassword
	c.GuestPassword = guestPassword
	return c.Save()
}

// GetVMRunPath 获取vmrun可执行文件路径
func (c *Config) GetVMRunPath() string {
	return c.VMwarePath
}

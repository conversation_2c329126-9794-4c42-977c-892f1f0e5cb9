package main

import (
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strings"
)

// IDGenerator 硬件ID生成器
type IDGenerator struct{}

// NewIDGenerator 创建新的ID生成器
func NewIDGenerator() *IDGenerator {
	return &IDGenerator{}
}

// GenerateID 基于硬件信息生成唯一ID
func (g *IDGenerator) GenerateID() (string, error) {
	// 获取硬件信息
	hwInfo, err := GetHardwareInfo()
	if err != nil {
		return "", fmt.Errorf("获取硬件信息失败: %w", err)
	}

	// 生成基础硬件字符串
	baseString := hwInfo.GetHardwareID()

	// 使用简单的SHA256哈希
	id := g.generateSimpleHash(baseString)

	return id, nil
}

// generateSecureID 生成安全的硬件ID
func (g *IDGenerator) generateSecureID(baseString string) string {
	// 第一层：MD5哈希
	md5Hash := md5.Sum([]byte(baseString))
	md5Str := hex.EncodeToString(md5Hash[:])
	
	// 第二层：SHA256哈希
	sha256Hash := sha256.Sum256([]byte(md5Str))
	sha256Str := hex.EncodeToString(sha256Hash[:])
	
	// 第三层：自定义混淆算法
	obfuscated := g.obfuscateString(sha256Str)
	
	// 格式化为更易读的格式（保持32字符长度）
	return g.formatID(obfuscated)
}

// obfuscateString 自定义字符串混淆算法
func (g *IDGenerator) obfuscateString(input string) string {
	if len(input) < 32 {
		return input
	}
	
	// 取前32个字符
	str := input[:32]
	runes := []rune(str)
	
	// 字符位置交换
	for i := 0; i < len(runes)-1; i += 2 {
		runes[i], runes[i+1] = runes[i+1], runes[i]
	}
	
	// 字符替换映射
	charMap := map[rune]rune{
		'0': 'g', '1': 'h', '2': 'j', '3': 'k',
		'4': 'm', '5': 'n', '6': 'p', '7': 'q',
		'8': 'r', '9': 's', 'a': '2', 'b': '3',
		'c': '4', 'd': '5', 'e': '6', 'f': '7',
	}
	
	// 应用字符映射
	for i, r := range runes {
		if mapped, exists := charMap[r]; exists {
			runes[i] = mapped
		}
	}
	
	return string(runes)
}

// formatID 格式化ID为易读格式
func (g *IDGenerator) formatID(id string) string {
	if len(id) < 32 {
		// 如果长度不足，用0填充
		id = id + strings.Repeat("0", 32-len(id))
	} else if len(id) > 32 {
		// 如果长度超出，截取前32位
		id = id[:32]
	}
	
	// 格式化为 XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX
	var formatted strings.Builder
	for i, char := range id {
		if i > 0 && i%4 == 0 {
			formatted.WriteRune('-')
		}
		formatted.WriteRune(char)
	}
	
	return strings.ToUpper(formatted.String())
}

// GenerateResetID 生成重置用的新ID（基于原ID进行变换）
func (g *IDGenerator) GenerateResetID(originalID string) string {
	// 移除分隔符
	cleanID := strings.ReplaceAll(originalID, "-", "")
	
	// 简单的字符位移算法
	runes := []rune(strings.ToLower(cleanID))
	for i := range runes {
		switch runes[i] {
		case 'g': runes[i] = '0'
		case 'h': runes[i] = '1'
		case 'j': runes[i] = '2'
		case 'k': runes[i] = '3'
		case 'm': runes[i] = '4'
		case 'n': runes[i] = '5'
		case 'p': runes[i] = '6'
		case 'q': runes[i] = '7'
		case 'r': runes[i] = '8'
		case 's': runes[i] = '9'
		case '2': runes[i] = 'a'
		case '3': runes[i] = 'b'
		case '4': runes[i] = 'c'
		case '5': runes[i] = 'd'
		case '6': runes[i] = 'e'
		case '7': runes[i] = 'f'
		}
	}
	
	// 反向位置交换
	for i := 0; i < len(runes)-1; i += 2 {
		runes[i], runes[i+1] = runes[i+1], runes[i]
	}
	
	// 重新哈希
	resetStr := string(runes)
	sha256Hash := sha256.Sum256([]byte(resetStr))
	newID := hex.EncodeToString(sha256Hash[:])[:32]
	
	return g.formatID(newID)
}

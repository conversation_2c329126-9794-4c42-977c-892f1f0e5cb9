package main

import (
	"fmt"
	"license-server-wrapper/crypto"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"syscall"
	"time"
)

// Windows API常量
const (
	EWX_SHUTDOWN = 0x00000001
	EWX_FORCE    = 0x00000004
)

// Windows API函数
var (
	user32          = syscall.NewLazyDLL("user32.dll")
	procExitWindows = user32.NewProc("ExitWindowsEx")
)

func main() {
	// 设置日志
	logFile := setupLogging()
	defer logFile.Close()

	log.Println("=== Guest Validator Go版本启动 ===")

	// 验证硬件ID
	if err := validateHardwareID(); err != nil {
		log.Printf("验证失败: %v", err)
		shutdownSystem()
		return
	}

	log.Println("验证成功，系统继续运行")

	// 重置guestVar（由guest端负责）
	resetGuestVar()
}

// setupLogging 设置日志记录
func setupLogging() *os.File {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		homeDir = os.TempDir()
	}

	logPath := filepath.Join(homeDir, ".lsw", "validator.log")
	os.MkdirAll(filepath.Dir(logPath), 0755)

	logFile, err := os.OpenFile(logPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		log.SetOutput(os.Stderr)
		return nil
	}

	log.SetOutput(logFile)
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	return logFile
}

// validateHardwareID 验证硬件ID
func validateHardwareID() error {
	log.Println("开始验证硬件ID...")

	// 获取加密的guestVar
	encryptedData, err := getGuestVar()
	if err != nil {
		return fmt.Errorf("获取guestVar失败: %w", err)
	}

	// 解析动态guestVar值
	hardwareID, timestamp, signature, err := parseDynamicGuestVar(encryptedData)
	if err != nil {
		return fmt.Errorf("解析guestVar失败: %w", err)
	}

	log.Printf("解析到硬件ID: %s", hardwareID[:8]+"...")
	log.Printf("时间戳: %d", timestamp)

	// 验证签名
	if !verifySignature(hardwareID, timestamp, signature) {
		return fmt.Errorf("签名验证失败")
	}

	// 验证时间戳（允许5分钟的时间差）
	currentTime := time.Now().Unix()
	if abs(currentTime-timestamp) > 300 { // 5分钟
		return fmt.Errorf("时间戳验证失败，时间差过大: %d秒", abs(currentTime-timestamp))
	}

	// 验证硬件ID
	homedir, err := os.UserHomeDir()
	id, err := os.ReadFile(filepath.Join(homedir, ".lsw", "hardware.id"))
	if err != nil {
		return fmt.Errorf("读取硬件ID文件失败: %w", err)
	}

	expectedID := strings.TrimSpace(string(id))
	if hardwareID != expectedID {
		return fmt.Errorf("硬件ID不匹配")
	}

	return nil
}

// getGuestVar 获取VMware guestVar
func getGuestVar() (string, error) {
	// 查找rpctool.exe
	rpctoolPath, err := findRPCTool()
	if err != nil {
		return "", fmt.Errorf("找不到rpctool.exe: %w", err)
	}

	log.Printf("使用rpctool路径: %s", rpctoolPath)

	// 执行rpctool命令
	cmd := exec.Command(rpctoolPath, "info-get guestinfo.hardware_id")
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("执行rpctool失败: %w", err)
	}

	guestVar := strings.TrimSpace(string(output))
	if guestVar == "" {
		return "", fmt.Errorf("guestVar为空")
	}

	return guestVar, nil
}

// findRPCTool 查找rpctool.exe
func findRPCTool() (string, error) {
	// 常见的VMware Tools路径
	paths := []string{
		`C:\Program Files\VMware\VMware Tools\rpctool.exe`,
		`C:\Program Files (x86)\VMware\VMware Tools\rpctool.exe`,
		`rpctool.exe`, // 在PATH中查找
	}

	for _, path := range paths {
		if _, err := os.Stat(path); err == nil {
			return path, nil
		}
	}

	return "", fmt.Errorf("未找到rpctool.exe")
}

// resetGuestVar 重置guestVar（由guest端负责）
func resetGuestVar() {
	log.Println("重置guestVar...")

	// 查找rpctool.exe
	rpctoolPath, err := findRPCTool()
	if err != nil {
		log.Printf("找不到rpctool.exe，无法重置: %v", err)
		return
	}

	// 生成随机重置值
	resetValue := fmt.Sprintf("RESET_%d", time.Now().Unix())

	// 重置guestVar
	cmd := exec.Command(rpctoolPath, "info-set guestinfo.hardware_id "+resetValue)
	if err := cmd.Run(); err != nil {
		log.Printf("重置guestVar失败: %v", err)
		return
	}

	log.Println("guestVar已重置")
}

// shutdownSystem 关闭系统
func shutdownSystem() {
	log.Println("验证失败，系统将关机")
	return

	// 尝试使用Windows API关机
	ret, _, err := procExitWindows.Call(
		uintptr(EWX_SHUTDOWN|EWX_FORCE),
		uintptr(0),
	)

	if ret == 0 {
		log.Printf("Windows API关机失败: %v", err)
		// 备用方案：使用shutdown命令
		cmd := exec.Command("shutdown", "/s", "/t", "0", "/f", "/c", "认证验证失败")
		if err := cmd.Run(); err != nil {
			log.Printf("shutdown命令失败: %v", err)
			// 最后的备用方案
			os.Exit(1)
		}
	}
}

// abs 计算绝对值
func abs(x int64) int64 {
	if x < 0 {
		return -x
	}
	return x
}

// parseDynamicGuestVar 解析动态guestVar值
func parseDynamicGuestVar(encryptedData string) (hardwareID string, timestamp int64, signature string, err error) {
	// DES解密
	decrypted, err := crypto.DecryptDES(encryptedData)
	if err != nil {
		return "", 0, "", fmt.Errorf("DES解密失败: %w", err)
	}

	// 解析数据：hardwareID|timestamp|signature
	parts := strings.Split(decrypted, "|")
	if len(parts) != 3 {
		return "", 0, "", fmt.Errorf("数据格式错误")
	}

	hardwareID = parts[0]
	timestamp, err = strconv.ParseInt(parts[1], 10, 64)
	if err != nil {
		return "", 0, "", fmt.Errorf("时间戳解析失败: %w", err)
	}
	signature = parts[2]

	return hardwareID, timestamp, signature, nil
}

// verifySignature 验证签名
func verifySignature(hardwareID string, timestamp int64, signature string) bool {
	expectedSignature := crypto.GenerateSignature(hardwareID, timestamp)
	return expectedSignature == signature
}

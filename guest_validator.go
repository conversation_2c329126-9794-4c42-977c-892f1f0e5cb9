package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"log"
	"os"
	"os/exec"
	"strings"
	"syscall"
	"time"
	"unsafe"
)

// 硬编码的预期硬件ID（在实际部署时需要替换）
const EXPECTED_HARDWARE_ID = "REPLACE_WITH_ACTUAL_HARDWARE_ID"

// 加密密钥种子（与主程序保持一致）
const encryptionSeed = "license-server-wrapper-2024-secure-key"

// Windows API 函数声明
var (
	kernel32         = syscall.NewLazyDLL("kernel32.dll")
	procExitWindows  = kernel32.NewProc("ExitWindowsEx")
	procGetLastError = kernel32.NewProc("GetLastError")
)

// 关机标志
const (
	EWX_SHUTDOWN = 0x00000001
	EWX_FORCE    = 0x00000004
)

func main() {
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	log.Println("认证服务验证程序启动...")

	// 等待系统完全启动
	time.Sleep(10 * time.Second)

	// 执行验证
	if err := validateHardwareID(); err != nil {
		log.Printf("硬件ID验证失败: %v", err)
		log.Println("系统将在5秒后关机...")
		time.Sleep(5 * time.Second)
		shutdownSystem()
		return
	}

	log.Println("硬件ID验证成功")
	
	// 验证成功后，清理痕迹
	cleanupTraces()
	
	log.Println("验证程序完成")
}

// validateHardwareID 验证硬件ID
func validateHardwareID() error {
	log.Println("开始验证硬件ID...")

	// 获取加密的硬件ID
	encryptedID, err := getHardwareIDFromHost()
	if err != nil {
		return fmt.Errorf("获取硬件ID失败: %w", err)
	}

	if encryptedID == "" {
		return fmt.Errorf("未找到硬件ID")
	}

	// 解密硬件ID
	decryptedID, err := decrypt(encryptedID)
	if err != nil {
		return fmt.Errorf("解密硬件ID失败: %w", err)
	}

	log.Printf("获取到硬件ID: %s", decryptedID[:8]+"...")

	// 验证硬件ID
	if !validateID(decryptedID, EXPECTED_HARDWARE_ID) {
		return fmt.Errorf("硬件ID不匹配")
	}

	return nil
}

// getHardwareIDFromHost 从宿主机获取硬件ID
func getHardwareIDFromHost() (string, error) {
	// 查找rpctool.exe的路径
	rpctoolPath, err := findRPCTool()
	if err != nil {
		return "", fmt.Errorf("找不到rpctool.exe: %w", err)
	}

	log.Printf("使用rpctool路径: %s", rpctoolPath)

	// 执行rpctool命令获取guestVar
	cmd := exec.Command(rpctoolPath, "info-get", "guestinfo.hardware_id")
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("执行rpctool失败: %w", err)
	}

	hardwareID := strings.TrimSpace(string(output))
	if hardwareID == "" {
		return "", fmt.Errorf("硬件ID为空")
	}

	return hardwareID, nil
}

// findRPCTool 查找rpctool.exe的路径
func findRPCTool() (string, error) {
	// 常见的VMware Tools安装路径
	possiblePaths := []string{
		`C:\Program Files\VMware\VMware Tools\rpctool.exe`,
		`C:\Program Files (x86)\VMware\VMware Tools\rpctool.exe`,
		`C:\VMware\VMware Tools\rpctool.exe`,
		`rpctool.exe`, // 如果在PATH中
	}

	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			return path, nil
		}
	}

	// 尝试在PATH中查找
	cmd := exec.Command("where", "rpctool.exe")
	output, err := cmd.Output()
	if err == nil {
		path := strings.TrimSpace(string(output))
		if path != "" {
			return path, nil
		}
	}

	return "", fmt.Errorf("未找到rpctool.exe")
}

// decrypt 解密字符串（与主程序保持一致）
func decrypt(ciphertext string) (string, error) {
	if ciphertext == "" {
		return "", nil
	}

	key := getEncryptionKey()

	// 解码base64
	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", fmt.Errorf("base64解码失败: %w", err)
	}

	// 创建AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("创建AES cipher失败: %w", err)
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("创建GCM模式失败: %w", err)
	}

	// 检查数据长度
	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("密文数据太短")
	}

	// 分离nonce和密文
	nonce, cipherData := data[:nonceSize], data[nonceSize:]

	// 解密数据
	plaintext, err := gcm.Open(nil, nonce, cipherData, nil)
	if err != nil {
		return "", fmt.Errorf("解密失败: %w", err)
	}

	return string(plaintext), nil
}

// getEncryptionKey 生成加密密钥（与主程序保持一致）
func getEncryptionKey() []byte {
	hash := sha256.Sum256([]byte(encryptionSeed))
	return hash[:]
}

// validateID 验证硬件ID是否匹配
func validateID(receivedID, expectedID string) bool {
	// 移除格式化字符
	cleanReceived := strings.ReplaceAll(strings.ToUpper(receivedID), "-", "")
	cleanExpected := strings.ReplaceAll(strings.ToUpper(expectedID), "-", "")

	return cleanReceived == cleanExpected
}

// shutdownSystem 关闭系统
func shutdownSystem() {
	log.Println("正在关闭系统...")

	// 尝试使用Windows API关机
	ret, _, err := procExitWindows.Call(
		uintptr(EWX_SHUTDOWN|EWX_FORCE),
		uintptr(0),
	)

	if ret == 0 {
		log.Printf("Windows API关机失败: %v", err)
		// 备用方案：使用shutdown命令
		cmd := exec.Command("shutdown", "/s", "/t", "0", "/f")
		if err := cmd.Run(); err != nil {
			log.Printf("shutdown命令失败: %v", err)
			// 最后的备用方案
			os.Exit(1)
		}
	}
}

// cleanupTraces 清理验证痕迹
func cleanupTraces() {
	log.Println("清理验证痕迹...")

	// 清理可能的临时文件
	tempFiles := []string{
		os.TempDir() + "\\vmware_validation.tmp",
		os.TempDir() + "\\hardware_check.tmp",
	}

	for _, file := range tempFiles {
		os.Remove(file)
	}

	// 清理注册表痕迹（如果需要）
	// 这里可以添加注册表清理代码

	log.Println("痕迹清理完成")
}

// 辅助函数：检查是否在VMware环境中运行
func isRunningInVMware() bool {
	// 检查VMware特征
	vmwareIndicators := []string{
		"VMware",
		"vmware",
		"VMWARE",
	}

	// 检查系统信息
	cmd := exec.Command("systeminfo")
	output, err := cmd.Output()
	if err != nil {
		return false
	}

	outputStr := string(output)
	for _, indicator := range vmwareIndicators {
		if strings.Contains(outputStr, indicator) {
			return true
		}
	}

	return false
}

// 初始化函数，在main之前执行一些检查
func init() {
	// 检查是否在VMware环境中
	if !isRunningInVMware() {
		log.Println("警告: 未检测到VMware环境")
	}

	// 设置日志输出到文件（可选）
	logFile := os.TempDir() + "\\auth_service_validation.log"
	if file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666); err == nil {
		log.SetOutput(file)
	}
}

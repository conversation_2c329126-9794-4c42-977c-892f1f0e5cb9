@echo off
setlocal enabledelayedexpansion

echo 正在构建Guest Validator Go版本...

cd guest_validator_go

REM 设置Go环境变量
set GOOS=windows
set GOARCH=amd64
set CGO_ENABLED=0

REM 构建可执行文件
go build -ldflags "-s -w" -o guest_validator.exe main.go

if %ERRORLEVEL% neq 0 (
    echo 构建失败！
    pause
    exit /b 1
)

echo 构建成功！生成文件: guest_validator.exe
echo 文件大小:
dir guest_validator.exe | findstr guest_validator.exe

cd ..

echo.
echo 构建完成！
pause
